import React from "react";
import { useAuctionStore } from "../store/useAuctionStore";

interface AuctionToolbarProps {
  currentLayout: string;
  onLayoutChange: (layout: string) => void;
}

function AuctionToolbar({
  currentLayout,
  onLayoutChange,
}: AuctionToolbarProps) {
  const {
    currentRound,
    isRunning,
    simulator,
    auctionResult,
    startNewAuction,
    runNextRound,
    runFullAuction,
  } = useAuctionStore();

  const layouts = [
    { id: "original", name: "Original" },
    { id: "flex", name: "FlexLayout" },
    { id: "golden", name: "Golden Layout" },
    { id: "reactMosaic", name: "React Mosaic" },
  ];

  return (
    <div className="flex w-full items-center gap-4 bg-white border-b border-gray-200 px-4 py-2">
      {/* --- Left Side Items --- */}

      {/* The Title (using a span as requested) */}
      <span className="text-xl font-bold text-gray-800 flex-shrink-0">
        🕐 Auction Simulator
      </span>

      {/* The Layout Buttons (no wrapper div, they are now independent) */}
      {layouts.map((layout) => (
        <button
          key={layout.id}
          onClick={() => onLayoutChange(layout.id)}
          className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
            currentLayout === layout.id
              ? "bg-blue-100 text-blue-700" // Simple style without the gray background
              : "text-gray-600 hover:text-gray-800 hover:bg-gray-100"
          }`}
        >
          {layout.name}
        </button>
      ))}

      {/* --- Right Side Items --- */}

      {/* 
    THE KEY: `ml-auto` (margin-left: auto)
    This is applied to the FIRST element you want pushed to the right. 
    It automatically takes up all available space to its left, shoving itself 
    and everything after it to the end of the flex container.
  */}
      <button
        onClick={startNewAuction}
        disabled={isRunning}
        className="ml-auto bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm flex-shrink-0"
      >
        🚀 Start New Auction
      </button>

      {/* The other control buttons just follow normally */}
      {simulator && !auctionResult && (
        <>
          <button
            onClick={runNextRound}
            disabled={isRunning || simulator.isAuctionComplete()}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm flex-shrink-0"
          >
            ⏭️ Next Round
          </button>

          <button
            onClick={runFullAuction}
            disabled={isRunning || simulator.isAuctionComplete()}
            className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm flex-shrink-0"
          >
            ⚡ Run to Completion
          </button>
        </>
      )}

      {/* The Round Indicator (using a span as a mini flex container to group its children) */}
      <span className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 px-3 py-1.5 rounded-lg flex-shrink-0">
        <span className="font-medium">Round:</span>
        <span className="bg-white px-2 py-0.5 rounded font-mono">
          {currentRound}
        </span>
        {isRunning && (
          <span className="text-blue-600 animate-pulse ml-2">● Running...</span>
        )}
      </span>
    </div>
  );
}

export default AuctionToolbar;
