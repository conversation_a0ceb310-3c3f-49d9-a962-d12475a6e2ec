import React from 'react';
import { useAuctionStore } from '../store/useAuctionStore';

interface AuctionToolbarProps {
  currentLayout: string;
  onLayoutChange: (layout: string) => void;
}

function AuctionToolbar({ currentLayout, onLayoutChange }: AuctionToolbarProps) {
  const {
    currentRound,
    isRunning,
    simulator,
    auctionResult,
    startNewAuction,
    runNextRound,
    runFullAuction
  } = useAuctionStore();

  const layouts = [
    { id: 'original', name: 'Original' },
    { id: 'flex', name: 'FlexLayout' },
    { id: 'golden', name: 'Golden Layout' },
    { id: 'reactMosaic', name: 'React Mosaic' }
  ];

  return (
    <div className="bg-white border-b border-gray-200 px-4 py-2">
      <div className="flex items-center justify-between">
        {/* Left side - Title and Layout buttons */}
        <div className="flex items-center gap-6">
          <h1 className="text-xl font-bold text-gray-800 flex items-center gap-2">
            🕐 Auction Simulator
          </h1>
          
          {/* Layout buttons */}
          <div className="flex items-center gap-1 bg-gray-100 rounded-lg p-1">
            {layouts.map((layout) => (
              <button
                key={layout.id}
                onClick={() => onLayoutChange(layout.id)}
                className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                  currentLayout === layout.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
                }`}
              >
                {layout.name}
              </button>
            ))}
          </div>
        </div>

        {/* Right side - Auction controls */}
        <div className="flex items-center gap-4">
          {/* Auction control buttons */}
          <div className="flex items-center gap-2">
            <button
              onClick={startNewAuction}
              disabled={isRunning}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm"
            >
              🚀 Start New Auction
            </button>

            {simulator && !auctionResult && (
              <>
                <button
                  onClick={runNextRound}
                  disabled={isRunning || simulator.isAuctionComplete()}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm"
                >
                  ⏭️ Next Round
                </button>

                <button
                  onClick={runFullAuction}
                  disabled={isRunning || simulator.isAuctionComplete()}
                  className="bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-medium py-1.5 px-4 rounded-lg transition-colors text-sm"
                >
                  ⚡ Run to Completion
                </button>
              </>
            )}
          </div>

          {/* Round indicator */}
          <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 px-3 py-1.5 rounded-lg">
            <span className="font-medium">Round:</span>
            <span className="bg-white px-2 py-0.5 rounded font-mono">{currentRound}</span>
            {isRunning && (
              <span className="text-blue-600 animate-pulse ml-2">● Running...</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default AuctionToolbar;
