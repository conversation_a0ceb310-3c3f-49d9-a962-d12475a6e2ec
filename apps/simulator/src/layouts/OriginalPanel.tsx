import { createComponent, type ComponentFactoryProps } from './shared/ComponentFactory';
import { useAuctionLogic } from './shared/useAuctionLogic';
import '../App.css';

function OriginalPanel() {
  const auctionLogic = useAuctionLogic();

  const props: ComponentFactoryProps = {
    simulator: auctionLogic.simulator,
    auctionResult: auctionLogic.auctionResult,
    rounds: auctionLogic.rounds,
    currentRound: auctionLogic.currentRound,
    isRunning: auctionLogic.isRunning,
    startNewAuction: auctionLogic.startNewAuction,
    runNextRound: auctionLogic.runNextRound,
    runFullAuction: auctionLogic.runFullAuction,
  };

  return (
    <div className="bg-gray-50" style={{ minHeight: "100%", paddingBottom: "2rem" }}>
      <div className="bg-white rounded-lg shadow-md p-4 mb-4 mx-4">
        {createComponent('controls', props)}
      </div>
      <div className="mx-4 space-y-4">
        {/* Row 1: Charts */}
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('price-timeline', props)}
          </div>
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('price-step-chart', props)}
          </div>
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('supply-demand', props)}
          </div>
        </div>

        {/* Row 2: Volume + Constraints + Participants */}
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('volume-analysis', props)}
          </div>
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('constraint-heatmap', props)}
          </div>
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('participants', props)}
          </div>
        </div>

        {/* Row 3: Tables */}
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('orders-table', props)}
          </div>
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('round-table', props)}
          </div>
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('quantity-constraints', props)}
          </div>
        </div>

        {/* Row 4: 3D + Allocation Flow */}
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('3d-ribbon', props)}
          </div>
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('sankey-flow', props)}
          </div>
          <div className="bg-white rounded-lg shadow-md p-4" style={{ minHeight: "350px" }}>
            {createComponent('allocation-flow', props)}
          </div>
        </div>
      </div>
    </div>
  );
}

export default OriginalPanel;